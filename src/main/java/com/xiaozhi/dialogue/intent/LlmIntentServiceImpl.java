package com.xiaozhi.dialogue.intent;

import com.fasterxml.jackson.core.type.TypeReference;
import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.dialogue.llm.factory.ChatModelFactory;

import com.xiaozhi.utils.JsonUtil;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.model.tool.ToolCallingChatOptions;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.xiaozhi.dialogue.llm.ChatService.TOOL_CONTEXT_SESSION_KEY;

@Service
public class LlmIntentServiceImpl implements IntentService {

    private static final Logger logger = LoggerFactory.getLogger(LlmIntentServiceImpl.class);

    private static final List<Message> chatHistory = new ArrayList<>() {{
        add(new SystemMessage(INTENT_RECOGNITION_SYSTEM_PROMPT));
    }};

    @Resource
    private ChatModelFactory chatModelFactory;

    private static final String INTENT_RECOGNITION_SYSTEM_PROMPT = """
            You are a very intelligent intent recognition assistant that analyzes user input and calls the corresponding tool,
            You must comply with the following regulations:

            Workflow:
            1. Understand user intent
            2. Find a tool that matches the user intent
            3. Extract the tool's parameters
            4. Call the corresponding tool

            Requirements:
            - Output must be JSON schema: {"function": "<function_name>", "response_message": "<function_result>"}
            - When using words such as larger or smaller, give a value based on the range of the parameter
            - If a tool is matched but required parameters are missing, ask the user for them and return: {"function": "missing_parameter", "response_message": "<what_you_need>"}.
            - If no tool matches the intent, return: {"function": "continue_chat"}


            Examples:

            Input: "现在几点了？"
            Action: call get_time function
            Return: {
              "function": {
                "name": "get_time",
                "response_message": "北京时间上午10:00"
              }
            }

            Input: "声音调整到 80%"
            Action: call self.audio_speaker.set_volume function
            Return: {
              "function": {
                "name": "self.audio_speaker.set_volume",
                "response_message": "音量已调整到<volume>%"
              }
            }

            Input: "播放《稻香》"
            Action: call func_play_music function with song_name 稻香
            Return: {
              "function": {
                "name": "func_play_music",
                "response_message": "开始播放歌曲<song_name>"
              }
            }
            """;

    @Override
    public String recognize(String text) {
        if (text == null || text.trim().isEmpty()) {
            logger.warn("输入文本为空");
            return createErrorResponse("输入文本为空");
        }

        try {
            // 1. 使用LLM进行意图识别
            String llmResponse = doRecognize(text);
            if (llmResponse == null || llmResponse.trim().isEmpty()) {
                logger.warn("LLM返回空响应");
                return createErrorResponse("LLM返回空响应");
            }

            // 2. 解析JSON响应
            Map<String, Object> intentResult = parseIntentResponse(llmResponse);
            if (intentResult == null) {
                logger.warn("无法解析LLM响应: {}", llmResponse);
                return createErrorResponse("无法解析LLM响应");
            }

            // 3. 提取函数信息
            Map<String, Object> functionInfo = extractFunctionInfo(intentResult);
            if (functionInfo == null) {
                logger.warn("无法提取函数信息: {}", intentResult);
                return createErrorResponse("无法提取函数信息");
            }

            String functionName = (String) functionInfo.get("name");
            if (functionName == null || functionName.trim().isEmpty()) {
                logger.warn("函数名为空");
                return createErrorResponse("函数名为空");
            }

            // 4. 返回意图识别结果的JSON字符串，不执行函数调用
            String intentJson = JsonUtil.toJson(intentResult);
            logger.info("意图识别成功 - 用户输入: {}, 函数: {}, 意图JSON: {}", text, functionName, intentJson);
            return intentJson;

        } catch (Exception e) {
            logger.error("意图识别失败 - 用户输入: {}", text, e);
            return createErrorResponse("意图识别失败: " + e.getMessage());
        }
    }

    /**
     * 调用LLM进行意图识别
     */
    private String doRecognize(String userInput) {
        try {
            // 获取意图识别模型
            var chatModel = chatModelFactory.takeIntentModel();
            if (chatModel == null) {
                logger.error("无法获取意图识别ChatModel");
                return null;
            }

            // 构建消息
            SystemMessage systemMessage = new SystemMessage(INTENT_RECOGNITION_SYSTEM_PROMPT);
            UserMessage userMessage = new UserMessage(userInput);

            // 创建Prompt
            Prompt prompt = new Prompt(List.of(systemMessage, userMessage));

            // 调用LLM
            ChatResponse response = chatModel.call(prompt);
            if (response == null || response.getResult() == null || response.getResult().getOutput() == null) {
                logger.error("LLM意图识别为空");
                return null;
            }

            String result = response.getResult().getOutput().getText();
            logger.debug("LLM意图识别响应: {}", result);
            return result;

        } catch (Exception e) {
            logger.error("LLM意图识别失败", e);
            return null;
        }
    }

    /**
     * 解析LLM的意图识别响应
     */
    private Map<String, Object> parseIntentResponse(String llmResponse) {
        try {
            // 清理响应文本，移除可能的markdown代码块标记
            String cleanResponse = llmResponse.trim();
            if (cleanResponse.startsWith("```json")) {
                cleanResponse = cleanResponse.substring(7);
            }
            if (cleanResponse.startsWith("```")) {
                cleanResponse = cleanResponse.substring(3);
            }
            if (cleanResponse.endsWith("```")) {
                cleanResponse = cleanResponse.substring(0, cleanResponse.length() - 3);
            }
            cleanResponse = cleanResponse.trim();

            // 解析JSON
            return JsonUtil.fromJson(cleanResponse, new TypeReference<>() {
            });

        } catch (Exception e) {
            logger.error("解析意图识别响应失败: {}", llmResponse, e);
            return null;
        }
    }

    /**
     * 从意图结果中提取函数信息
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> extractFunctionInfo(Map<String, Object> intentResult) {
        try {
            Object functionObj = intentResult.get("function");
            if (functionObj instanceof Map) {
                return (Map<String, Object>) functionObj;
            }
            return null;
        } catch (Exception e) {
            logger.error("提取函数信息失败", e);
            return null;
        }
    }

    /**
     * 创建错误响应
     */
    private String createErrorResponse(String errorMessage) {
        // return "意图识别错误: " + errorMessage;
        return """
                {
                  "function": {
                    "name": "continue_chat"
                  }
                }
                """;
    }

    @Override
    public String recognize(ChatSession session, String text) {
        if (text == null || text.trim().isEmpty()) {
            logger.warn("输入文本为空");
            return createErrorResponse("输入文本为空");
        }

        try {
            // 2. 使用带工具信息的LLM进行意图识别
            var llmResponse = doRecognizeWithTools(session, text);

            // 3. 解析JSON响应
            Map<String, Object> intentResult = parseIntentResponse(llmResponse.getResult().getOutput().getText());
            if (intentResult == null) {
                logger.warn("无法解析LLM响应: {}", llmResponse);
                return createErrorResponse("无法解析LLM响应");
            }

            // 4. 提取函数信息
            Map<String, Object> functionInfo = extractFunctionInfo(intentResult);
            if (functionInfo == null) {
                logger.warn("无法提取函数信息: {}", intentResult);
                return createErrorResponse("无法提取函数信息");
            }

            String functionName = (String) functionInfo.get("name");
            if (functionName == null || functionName.trim().isEmpty()) {
                logger.warn("函数名为空");
                return createErrorResponse("函数名为空");
            }

            if (!functionName.equals("continue_chat")) {
                chatHistory.add(new UserMessage(text));
                chatHistory.add(llmResponse.getResult().getOutput());
            }

            // 5. 返回意图识别结果的JSON字符串，不执行函数调用
            String intentJson = JsonUtil.toJson(intentResult);
            logger.info("意图识别成功 - 会话: {}, 用户输入: {}, 函数: {}, 意图JSON: {}",
                    session.getSessionId(), text, functionName, intentJson);
            return intentJson;
        } catch (Exception e) {
            logger.error("意图识别异常 - 会话: {}, 用户输入: {}, 错误: {}",
                    session.getSessionId(), text, e.getMessage(), e);
            return createErrorResponse("意图识别异常: " + e.getMessage());
        }
    }

    /**
     * 构建可用工具的描述信息
     */
    private String buildAvailableToolsDescription(List<ToolCallback> toolCallbacks) {
        if (toolCallbacks == null || toolCallbacks.isEmpty()) {
            return "无可用工具";
        }

        return toolCallbacks.stream()
                .map(tool -> {
                    String name = tool.getToolDefinition().name();
                    String description = tool.getToolDefinition().description();
                    return String.format("- %s: %s", name, description != null ? description : "无描述");
                })
                .collect(Collectors.joining("\n"));
    }

    /**
     * 使用带工具信息的LLM进行意图识别
     */
    private ChatResponse doRecognizeWithTools(ChatSession session, String text) {
        try {
            var intentModel = chatModelFactory.takeIntentModel();
            if (intentModel == null) {
                logger.error("无法获取意图识别模型");
                return null;
            }

            ChatOptions chatOptions = ToolCallingChatOptions.builder()
                    .toolCallbacks(session.getToolCallbacks())
                    .toolContext(TOOL_CONTEXT_SESSION_KEY, session)
                    .build();

            UserMessage userMessage = new UserMessage(text);
            var messages = new ArrayList<>(chatHistory);
            messages.add(userMessage);
            logger.info("Message history list:");
            messages.forEach(m -> logger.info("Message {}", m));

            Prompt prompt = new Prompt(messages, chatOptions);
            ChatResponse response = intentModel.call(prompt);

            logger.info("Intent finish reason is {}", response.getResult().getMetadata().getFinishReason());

            if (response == null || response.getResult() == null || response.getResult().getOutput() == null) {
                logger.error("LLM响应为空");
                return null;
            }

            String content = response.getResult().getOutput().getText();
            logger.debug("LLM原始响应: {}", content);

            return response;

        } catch (Exception e) {
            logger.error("调用LLM进行意图识别失败: {}", e.getMessage(), e);
            return null;
        }
    }
}
