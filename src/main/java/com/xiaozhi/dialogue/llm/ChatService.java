package com.xiaozhi.dialogue.llm;

import com.xiaozhi.communication.common.ChatSession;
import com.xiaozhi.dialogue.domain.Sentence;
import com.xiaozhi.dialogue.llm.factory.ChatModelFactory;
import com.xiaozhi.dialogue.llm.memory.ChatMemory;
import com.xiaozhi.dto.ChatParams;
import com.xiaozhi.dto.CozeChatParams;
import com.xiaozhi.entity.SysDevice;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.messages.*;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.model.tool.ToolCallingChatOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 负责管理和协调LLM相关功能
 * TODO 重构：改成Domain Entity: ChatRole(聊天角色)，管理对话历史记录，管理对话工具调用等。
 */
@Service
public class ChatService {
    private static final Logger logger = LoggerFactory.getLogger(ChatService.class);

    public static final String TOOL_CONTEXT_SESSION_KEY = "session";

    // 最小句子长度（字符数）
    private static final int MIN_SENTENCE_LENGTH = 5;

    @Resource
    private ChatMemory chatMemoryStore;

    // TODO 移到构建者模式，由连接通过认证，可正常对话时，创建实例，构建好一个完整的Role.
    @Resource
    private ChatModelFactory chatModelFactory;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 处理用户查询（同步方式）
     *
     * @param session 会话信息
     * @param message 用户消息
     * @param useTool 是否使用函数调用
     * @return 模型回复
     */
    public String chat(ChatSession session, String message, boolean useTool) {
        try {

            // 获取ChatModel
            var chatModel = chatModelFactory.takeChatModel(
                    session.getCurrentRole().getModelConfig(),
                    session.getSessionId(),
                    session.getSysDevice().getDeviceId()
            );

            var chatOptions = ToolCallingChatOptions.builder()
                    .toolCallbacks(useTool ? session.getToolCallbacks() : new ArrayList<>())
                    .toolContext(TOOL_CONTEXT_SESSION_KEY, session)
                    .build();

            var userMessage = new UserMessage(message);
            var messages = session.getConversation().prompt(userMessage);
            var prompt = new Prompt(messages, chatOptions);

            var chatResponse = chatModel.call(prompt);
            if (chatResponse == null || chatResponse.getResult().getOutput().getText() == null) {
                logger.warn("模型响应为空或无生成内容");
                return "抱歉，我在处理您的请求时遇到了问题。请稍后再试。";
            }
            var assistantMessage = chatResponse.getResult().getOutput();

            Thread.startVirtualThread(() -> {// 异步持久化
                // 保存AI消息，会被持久化至数据库。
                session.getConversation().addMessage(userMessage, session.getUserTimeMillis(), assistantMessage,
                        session.getAssistantTimeMillis());
            });
            return assistantMessage.getText();

        } catch (Exception e) {
            logger.error("处理查询时出错: {}", e.getMessage(), e);
            return "抱歉，我在处理您的请求时遇到了问题。请稍后再试。";
        }
    }

    /**
     * 处理用户查询（流式方式）
     *
     * @param params  用户消息
     * @param useTool 是否使用函数调用
     */
    public Flux<ChatResponse> chatStream(ChatSession session, ChatParams params, boolean useTool) {
        var modelConfig = session.getCurrentRole().getModelConfig();
        var chatModel = chatModelFactory.takeChatModel(
                modelConfig,
                session.getSessionId(),
                session.getSysDevice().getDeviceId()
        );

        if (modelConfig.getProvider().equalsIgnoreCase("coze")) {
            var conversationId = (String)session.getAttribute("coze_conversation_id");
            if (conversationId == null) {
                conversationId = chatModel.getConversationId(params);
                session.setAttribute("coze_conversation_id", conversationId);
            }

            var fields = redisTemplate.opsForHash()
                    .multiGet(STR."xiaozhi:devices:\{session.getSysDevice().getDeviceId()}:info", List.of("nickname", "level", "age"));

            var customVariables = Map.of(
                    "nickname", Optional.ofNullable(fields.getFirst()).map(Object::toString).orElse(""),
                    "level",  Optional.ofNullable(fields.get(1)).map(Object::toString).orElse("A1"),
                    "age", Optional.ofNullable(fields.getLast()).map(Object::toString).orElse("8")
            );

            params = new CozeChatParams()
                    .setUserId(session.getSessionId())
                    .setConversationId(conversationId)
                    .setCustomVariables(customVariables)
                    .setMessage(params.getMessage());
        } else {

        }

        var chatOptions = ToolCallingChatOptions.builder()
                .toolCallbacks(useTool ? session.getToolCallbacks() : new ArrayList<>())
                .toolContext(TOOL_CONTEXT_SESSION_KEY, session)
                .build();

        var userMessage = new UserMessage(params.getMessage());
        var messages = session.getAdditionalMessages().isEmpty()
                ? session.getConversation().prompt(userMessage)
                : session.getConversation().prompt(new AssistantMessage(session.getAdditionalMessages().removeFirst()), userMessage);
        var prompt = new Prompt(messages, chatOptions);
        // 调用实际的流式聊天方法
        return chatModel.stream(prompt, params);
    }

    public Flux<Sentence> chatSentenceStream(ChatSession session, ChatParams params, boolean useTool) {
        try {
            SysDevice device = session.getSysDevice();
            // device.setSessionId(session.getSessionId());

            final SentenceProcessor.StatefulSentenceProcessor processor = new SentenceProcessor.StatefulSentenceProcessor();
            final AtomicInteger sentenceCount = new AtomicInteger(0);

            // 调用现有的流式方法
            return chatStream(session, params, useTool)
                    .map(chatResponse -> {
                        String token = chatResponse.getResult() == null
                                || chatResponse.getResult().getOutput() == null
                                || chatResponse.getResult().getOutput().getText() == null ? ""
                                : chatResponse.getResult().getOutput().getText();
                        return token;
                    })
                    .filter(it -> !it.isEmpty())
                    .flatMap(token -> {
                        String sentence = processor.processToken(token);
                        if (sentence != null && !sentence.isEmpty()) {
                            return Flux.just(sentence);
                        }
                        return Flux.empty();
                    })
                    .map(text -> {
                        int seq = sentenceCount.incrementAndGet();
                        return new Sentence(seq, text, seq == 1, false);
                    })
                    .concatWith(Flux.defer(() -> {
                        // 获取最后的句子并标记为最后一句
                        String lastSentence = processor.getLastSentence();
                        int finalSeq = sentenceCount.incrementAndGet();

                        if (lastSentence != null && !lastSentence.isEmpty()) {
                            return Flux.just(new Sentence(finalSeq, lastSentence, finalSeq == 1, true));
                        }
                        // 如果没有剩余句子，发送一个空的结束标记
                        return Flux.just(new Sentence(finalSeq, "", finalSeq == 1, true));
                    }));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("处理LLM时出错: {}", e.getMessage(), e);
            // 返回错误句子
            return Flux.just(new Sentence(1, "抱歉，我在处理您的请求时遇到了问题。", true, true));
        }
    }

    /**
     * 清除设备缓存
     *
     * @param deviceId 设备ID
     */
    public void clearMessageCache(String deviceId) {
        chatMemoryStore.clearMessages(deviceId);
    }

    /**
     * 判断文本是否包含实质性内容（不仅仅是空白字符或标点符号）
     *
     * @param text 要检查的文本
     * @return 是否包含实质性内容
     */
    private boolean containsSubstantialContent(String text) {
        if (text == null || text.trim().length() < MIN_SENTENCE_LENGTH) {
            return false;
        }

        // 移除所有标点符号和空白字符后，检查是否还有内容
        String stripped = text.replaceAll("[\\p{P}\\s]", "");
        return stripped.length() >= 2; // 至少有两个非标点非空白字符
    }
}