package com.xiaozhi.dialogue.llm.memory;

import com.xiaozhi.entity.SysMessage;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SummaryChatMemory implements ChatMemory {
    @Override
    public void addMessage(String deviceId, String sessionId, String sender, String content, Integer roleId, String messageType, Long timeMillis) {

    }

    @Override
    public List<SysMessage> getMessages(String deviceId, String messageType, Integer limit) {
        return List.of();
    }

    @Override
    public void clearMessages(String deviceId) {

    }
}
